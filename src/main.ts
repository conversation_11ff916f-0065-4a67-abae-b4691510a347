import { ValidationPipe } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import { NestFactory } from '@nestjs/core'
// import rateLimit from 'express-rate-limit'
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger'
import { json, urlencoded } from 'express'
import { AllExceptionsFilter } from './common/filters'
import { AppModule } from './modules/app.module'
async function bootstrap() {
  const app = await NestFactory.create(AppModule)
  const configService = app.get(ConfigService)
  const port = configService.get<string>('PORT')
  app.enableCors({
    origin: '*',
  });
  // app
  //   .use
  // rateLimit({
  //   windowMs: 1 * 60 * 1000, // 1 minutes
  //   max: 100000, // limit each IP to 100,000 requests per windowMs
  // }),
  // ()
  app.use(json({ limit: '10mb' }))
  app.use(urlencoded({ extended: true, limit: '10mb' }))

  app.useGlobalPipes(new ValidationPipe({ transform: true }))
  // const httpServer = app.get(HttpService)
  app.useGlobalFilters(new AllExceptionsFilter(configService))

  // await app.listen(port || 4000)
  // console.log(`Application is running on: ${await app.getUrl()}`)

  // Set
  const options = new DocumentBuilder()
    .setTitle('Document API')
    .setDescription('The API description')
    .setVersion('1.0')
    .addBearerAuth()
    .build()
  const document = SwaggerModule.createDocument(app, options, {
    include: [], // don't include, say, BearsModule
  })
  SwaggerModule.setup('api/public-api-for-scale', app, document)

  await app.listen(port || 4000)
  console.log(`Application is running on: ${await app.getUrl()}`)
}
bootstrap()
