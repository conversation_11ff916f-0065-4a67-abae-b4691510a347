import { HttpService } from '@nestjs/axios'
import { UnauthorizedException } from '@nestjs/common'
import { lastValueFrom } from 'rxjs'
/**
 * Helper gọi API public cho hệ thống MES.
 * Hỗ trợ POST/GET
 */
class ApiPublicHelper {
  constructor(private httpService: HttpService) {}

  /**
   * Hàm POST dữ liệu lên API
   * @param url Địa chỉ endpoint API
   * @param data Dữ liệu gửi lên
   */
  public async callAPI(url: string, data: any) {
    try {
      await new Promise((resolve, reject) => {
        const request = this.httpService.post(url, data)
        lastValueFrom(request)
          .then((res) => {
            resolve(res.data)
          })
          .catch((err: any) => {
            reject(err.response)
          })
      })
    } catch (error) {
      console.log(error)
    }
  }

  /**
   * Hàm POST dữ liệu lên API, có thể kèm header xác thực (x-header)
   * <PERSON><PERSON><PERSON> không có key_secret thì bỏ qua header x-header
   * @param data Dữ liệu gửi lên
   * @param apiUrl Địa chỉ endpoint API
   * @returns Promise trả về dữ liệu từ API
   */
  processCallApiHelper(data: any, apiUrl: string): Promise<any> {
    const key_secret = process.env.API_SCALE_SECRET
    if (!key_secret) throw new UnauthorizedException('API_SCALE_SECRET')

    const header = { headers: { origin: '', 'x-header': key_secret } }

    return lastValueFrom(this.httpService.post(apiUrl, data, header))
      .then((res) => res?.data)
      .catch((err: any) => {
        console.error('API call failed', err?.response || err);
        throw err;
      });
  }

  /**
   * Hàm GET dữ liệu từ API, có thể kèm header xác thực (x-header)
   * Nếu không có key_secret thì bỏ qua header x-header
   * @param apiUrl Địa chỉ endpoint API
   * @param params Tham số query (nếu có)
   * @returns Promise trả về dữ liệu từ API
   */
  processCallApiGetHelper(apiUrl: string, params: any = {}): Promise<any> {
    const key_secret = process.env.API_SCALE_SECRET;
    const headers: any = { origin: '' };
    if (key_secret) {
      headers['x-header'] = key_secret;
    } else {
      console.warn('⚠️ Không có API_SCALE_SECRET, gọi API không có header key.');
    }
    const config = {
      headers,
      params
    };
    return lastValueFrom(this.httpService.get(apiUrl, config))
      .then((res) => res.data)
      .catch((err: any) => {
        console.error('API GET call failed', err?.response || err);
        throw err;
      });
  }

}

export const apiPublicHelper = new ApiPublicHelper(new HttpService())
