{
  "compilerOptions": {
    "lib": [
      "es5",
      "es6"
    ],
    "target": "es2017",
    "module": "commonjs",
    "moduleResolution": "node",
    "outDir": "./dist",
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "sourceMap": true,
    "resolveJsonModule": true,
    "declaration": true /* Generates corresponding '.d.ts' file. */,
    //"outDir": "./dist" /* Redirect output structure to the directory. */,
    "removeComments": true /* Do not emit comments to output. */,
    "strict": true /* Enable all strict type-checking options. */,
    "noImplicitAny": true /* Raise error on expressions and declarations with an implied 'any' type. */,
    "strictNullChecks": true /* Enable strict null checks. */,
    "strictFunctionTypes": true /* Enable strict checking of function types. */,
    "strictPropertyInitialization": false /* Enable strict checking of property initialization in classes. */,
    "baseUrl": "./" /* Base directory to resolve non-absolute module names. */,
    "allowSyntheticDefaultImports": true /* Allow default imports from modules with no default export. This does not affect code emit, just typechecking. */,
    "esModuleInterop": true /* Enables emit interoperability between CommonJS and ES Modules via creation of namespace objects for all imports. Implies 'allowSyntheticDefaultImports'. */,
    "forceConsistentCasingInFileNames": true /* Disallow inconsistently-cased references to the same file. */,
    "noLib": false,
    "incremental": true,
    "skipLibCheck": true,
    "strictBindCallApply": false,
    "noFallthroughCasesInSwitch": false
  },
  "ts-node": {
    "transpileOnly": true
  },
  "include": [
    "src/**/*",
    "types"
  ],
  "exclude": [
    "node_modules",
    "**/*.spec.ts"
  ]
}