# Add directories or file patterns to ignore during indexing (e.g. foo/ or *.csv)
deloyment.yaml
deloyment-sitewise.yaml

.env

node_modules

.serverless
.next
.git
.gitignore

# Dependency directories
bower_components/
jspm_packages/
package-lock.json
yarn.lock

# IDE and editor files
.idea/
.vscode/
*.swp
*.swo
.DS_Store
Thumbs.db

# Build outputs
dist/
build/
out/
coverage/

# Logs and databases
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
*.sqlite
*.db

# Environment and config files
.env.*
config.js
*.config.js
secrets.*

# Cache and temp files
.cache/
.temp/
.tmp/
*.tmp
*.temp

# Test files
__tests__/
test/
tests/
*.test.js
*.spec.js

docker-compose.yml