{"name": "ntvv-api", "version": "0.0.1", "description": "", "private": true, "author": "ApeTechs", "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "prestart:prod": "rimraf dist && tsc", "start:hmr": "node dist/server", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js", "typeorm:migrate": "yarn typeorm migration:generate -n", "typeorm:run": "yarn typeorm migration:run", "typeorm:revert": "yarn typeorm migration:revert"}, "dependencies": {"@nestjs/axios": "^2.0.0", "@nestjs/common": "^8.0.0", "@nestjs/config": "^2.0.0", "@nestjs/core": "^8.0.0", "@nestjs/jwt": "^8.0.0", "@nestjs/passport": "^8.2.1", "@nestjs/platform-express": "^8.0.0", "@nestjs/schedule": "^2.0.1", "@nestjs/swagger": "^6.2.1", "@nestjs/typeorm": "^8.0.3", "@types/cron": "^1.7.3", "aws-sdk": "^2.1136.0", "bcrypt": "^5.1.0", "class-transformer": "^0.5.1", "class-validator": "^0.13.2", "express-rate-limit": "^5.2.3", "firebase-admin": "^10.2.0", "flatted": "^3.2.6", "md5": "^2.3.0", "moment": "^2.29.3", "mysql2": "^2.3.3", "nanoid": "^3.1.20", "passport": "^0.4.1", "passport-jwt": "^4.0.0", "passport-local": "^1.0.0", "qs": "^6.10.1", "read-vn-number": "^4.0.1", "reflect-metadata": "^0.1.13", "rimraf": "^3.0.2", "rxjs": "^7.2.0", "typeorm": "0.2.45", "typeorm-transactional-cls-hooked": "^0.1.21"}, "devDependencies": {"@nestjs/cli": "^8.0.0", "@nestjs/schematics": "^8.0.0", "@types/bcrypt": "^3.0.0", "@types/express": "^4.17.13", "@types/jest": "27.4.1", "@types/nanoid": "^2.1.0", "@types/node": "^16.0.0", "@types/passport-jwt": "^3.0.3", "@types/passport-local": "^1.0.33", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^27.2.5", "prettier": "^2.3.2", "source-map-support": "^0.5.20", "ts-jest": "^27.0.3", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "^3.10.1", "typescript": "^4.3.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}